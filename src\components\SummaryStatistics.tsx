'use client';

import React, { useState, useMemo } from 'react';
import { EvaluatedTicket } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, CheckCircle, XCircle, FileText, Filter } from 'lucide-react';

interface SummaryStatisticsProps {
  results: EvaluatedTicket[];
}

/**
 * Summary statistics component showing evaluation results overview
 * Displays total processed, successful, failed counts and ranking distribution
 */
export const SummaryStatistics: React.FC<SummaryStatisticsProps> = ({ results }) => {
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  // Calculate basic statistics
  const stats = useMemo(() => {
    const total = results.length;
    const successful = results.filter(r => r.processingStatus === 'complete').length;
    const failed = results.filter(r => r.processingStatus === 'failed').length;
    const pending = results.filter(r => r.processingStatus === 'pending' || r.processingStatus === 'in-progress').length;

    return { total, successful, failed, pending };
  }, [results]);

  // Get unique services for filtering
  const services = useMemo(() => {
    const uniqueServices = Array.from(new Set(results.map(r => r.Service)));
    return uniqueServices.sort();
  }, [results]);

  // Filter results by selected services
  const filteredResults = useMemo(() => {
    if (selectedServices.length === 0) return results;
    return results.filter(r => selectedServices.includes(r.Service));
  }, [results, selectedServices]);

  // Calculate ranking distribution
  const rankingDistribution = useMemo(() => {
    const distribution = {
      'Excellent': 0,
      'Good': 0,
      'Average': 0,
      'Below Average': 0,
      'Poor': 0,
      'Error': 0
    };

    filteredResults.forEach(result => {
      if (result.processingStatus === 'complete' && result.ranking) {
        distribution[result.ranking]++;
      }
    });

    return distribution;
  }, [filteredResults]);

  const toggleService = (service: string) => {
    setSelectedServices(prev => 
      prev.includes(service) 
        ? prev.filter(s => s !== service)
        : [...prev, service]
    );
  };

  const clearFilters = () => {
    setSelectedServices([]);
  };

  const getRankingColor = (ranking: string) => {
    const colors = {
      'Excellent': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'Good': 'bg-green-100 text-green-800 border-green-200',
      'Average': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'Below Average': 'bg-orange-100 text-orange-800 border-orange-200',
      'Poor': 'bg-red-100 text-red-800 border-red-200',
      'Error': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[ranking as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Processed</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Incident tickets uploaded
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successful</CardTitle>
            <CheckCircle className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600">{stats.successful}</div>
            <p className="text-xs text-muted-foreground">
              Successfully evaluated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <p className="text-xs text-muted-foreground">
              Evaluation failed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <BarChart3 className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Currently processing
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Ranking Distribution */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Quality Ranking Distribution
              </CardTitle>
              <CardDescription>
                Distribution of evaluation rankings
                {selectedServices.length > 0 && ` (filtered by ${selectedServices.length} service${selectedServices.length > 1 ? 's' : ''})`}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                disabled={selectedServices.length === 0}
              >
                <Filter className="h-4 w-4 mr-1" />
                Clear Filters
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Service Filter */}
          <div>
            <p className="text-sm font-medium mb-2">Filter by Service:</p>
            <div className="flex flex-wrap gap-2">
              {services.map(service => (
                <Badge
                  key={service}
                  variant={selectedServices.includes(service) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => toggleService(service)}
                >
                  {service}
                </Badge>
              ))}
            </div>
          </div>

          {/* Ranking Chart */}
          <div className="space-y-3">
            {Object.entries(rankingDistribution).map(([ranking, count]) => {
              const percentage = filteredResults.length > 0 ? (count / filteredResults.length) * 100 : 0;
              return (
                <div key={ranking} className="flex items-center gap-3">
                  <div className="w-24 text-sm font-medium">{ranking}</div>
                  <div className="flex-1 bg-muted rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getRankingColor(ranking).split(' ')[0]}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <div className="w-16 text-right">
                    <span className="text-sm font-medium">{count}</span>
                    <span className="text-xs text-muted-foreground ml-1">
                      ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
