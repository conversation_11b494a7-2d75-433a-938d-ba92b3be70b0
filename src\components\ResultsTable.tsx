'use client';

import React, { useState, useMemo } from 'react';
import { EvaluatedTicket } from '@/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination';
import {
  Search,
  Download,
  Eye,
  EyeOff,
  Calendar,
  Building,
  MessageSquare,
  Star
} from 'lucide-react';

interface ResultsTableProps {
  results: EvaluatedTicket[];
  loading?: boolean;
  error?: string;
}

/**
 * Professional results table component displaying evaluation results
 * Shows ID, service, ranking, and review comments with search functionality
 */
export const ResultsTable: React.FC<ResultsTableProps> = ({
  results,
  loading = false,
  error
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRanking, setSelectedRanking] = useState<string>('');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Filter results based on search and ranking filter
  const filteredResults = useMemo(() => {
    return results.filter(result => {
      const matchesSearch = searchTerm === '' ||
        result.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.Service.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.Summary.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesRanking = selectedRanking === '' || result.ranking === selectedRanking;

      return matchesSearch && matchesRanking;
    });
  }, [results, searchTerm, selectedRanking]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedResults = filteredResults.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedRanking]);

  // Export functionality
  const exportToCSV = () => {
    const headers = [
      'ID',
      'Impact Date',
      'Service',
      'Problem Service',
      'Summary',
      'Business Impact',
      'Instructions',
      'Technical Details',
      'Ranking',
      'Review Comments',
      'Processing Status',
      'Evaluated At'
    ];

    const csvData = filteredResults.map(result => [
      result.id,
      result.ImpactDate,
      result.Service,
      result.ProblemService,
      `"${result.Summary.replace(/"/g, '""')}"`, // Escape quotes in CSV
      `"${result.BusinessImpact.replace(/"/g, '""')}"`,
      `"${result.Instructions.replace(/"/g, '""')}"`,
      `"${result.TechnicalDetails.replace(/"/g, '""')}"`,
      result.ranking,
      `"${result.reviewComments.replace(/"/g, '""')}"`,
      result.processingStatus,
      result.evaluatedAt
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `flash-qa-results-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const toggleRowExpansion = (ticketId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(ticketId)) {
      newExpanded.delete(ticketId);
    } else {
      newExpanded.add(ticketId);
    }
    setExpandedRows(newExpanded);
  };

  const getRankingColor = (ranking: string) => {
    const colors = {
      'Excellent': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'Good': 'bg-green-100 text-green-800 border-green-200',
      'Average': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'Below Average': 'bg-orange-100 text-orange-800 border-orange-200',
      'Poor': 'bg-red-100 text-red-800 border-red-200',
      'Error': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[ranking as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getRankingIcon = (ranking: string) => {
    const icons = {
      'Excellent': '⭐⭐⭐⭐⭐',
      'Good': '⭐⭐⭐⭐',
      'Average': '⭐⭐⭐',
      'Below Average': '⭐⭐',
      'Poor': '⭐',
      'Error': '❌'
    };
    return icons[ranking as keyof typeof icons] || '❓';
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading evaluation results...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-destructive mb-2">Error loading results</p>
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!results || results.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">No evaluation results to display</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const rankings = ['Excellent', 'Good', 'Average', 'Below Average', 'Poor', 'Error'];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Evaluation Results
            </CardTitle>
            <CardDescription>
              Showing {startIndex + 1}-{Math.min(endIndex, filteredResults.length)} of {filteredResults.length} tickets
              {filteredResults.length !== results.length && ` (filtered from ${results.length} total)`}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={exportToCSV}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by ID, service, or summary..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="w-full sm:w-48">
            <select
              value={selectedRanking}
              onChange={(e) => setSelectedRanking(e.target.value)}
              className="w-full h-9 px-3 py-1 text-sm border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="">All Rankings</option>
              {rankings.map(ranking => (
                <option key={ranking} value={ranking}>{ranking}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Results Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">ID</TableHead>
                <TableHead>Service</TableHead>
                <TableHead>Summary</TableHead>
                <TableHead className="w-[140px]">Ranking</TableHead>
                <TableHead className="w-[120px]">Impact Date</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedResults.map((result) => (
                <React.Fragment key={result.id}>
                  <TableRow className="hover:bg-muted/50">
                    <TableCell className="font-medium">{result.id}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>{result.Service}</span>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="truncate" title={result.Summary}>
                        {result.Summary}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRankingColor(result.ranking)}>
                        <span className="mr-1">{getRankingIcon(result.ranking)}</span>
                        {result.ranking}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {formatDate(result.ImpactDate)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleRowExpansion(result.id)}
                      >
                        {expandedRows.has(result.id) ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                  
                  {/* Expanded Row with Review Comments */}
                  {expandedRows.has(result.id) && (
                    <TableRow>
                      <TableCell colSpan={6} className="bg-muted/30">
                        <div className="py-4 space-y-4">
                          <div>
                            <h4 className="font-medium mb-2 flex items-center gap-2">
                              <MessageSquare className="h-4 w-4" />
                              Review Comments
                            </h4>
                            <p className="text-sm text-muted-foreground bg-background p-3 rounded border">
                              {result.reviewComments || 'No review comments available.'}
                            </p>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium">Problem Service:</span>
                              <p className="text-muted-foreground">{result.ProblemService}</p>
                            </div>
                            <div>
                              <span className="font-medium">Business Impact:</span>
                              <p className="text-muted-foreground">{result.BusinessImpact}</p>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredResults.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No results match your search criteria.</p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage > 1) setCurrentPage(currentPage - 1);
                    }}
                    className={currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setCurrentPage(pageNum);
                        }}
                        isActive={currentPage === pageNum}
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                    }}
                    className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
