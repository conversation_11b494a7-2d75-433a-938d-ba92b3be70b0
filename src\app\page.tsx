'use client';

import React, { useState } from 'react';
import FileUpload from '@/components/FileUpload';
import { ResultsTable } from '@/components/ResultsTable';
import { SummaryStatistics } from '@/components/SummaryStatistics';
import { UploadResponse, EvaluatedTicket } from '@/types';

export default function HomePage() {
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<EvaluatedTicket[] | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const handleUploadSuccess = (response: UploadResponse) => {
    setResults(response.evaluatedTickets || []);
    setFileName(response.fileName || 'Unknown file');
    setError(null);
    setIsLoading(false);
  };

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
    setResults(null);
    setIsLoading(false);
  };

  const handleUploadStart = () => {
    setIsLoading(true);
    setError(null);
    setResults(null);
  };

  const resetUpload = () => {
    setError(null);
    setResults(null);
    setFileName('');
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-6">
              <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Flash QA
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-2">
              AI-Powered Incident Analysis & Categorization
            </p>
            <p className="text-lg text-gray-500 max-w-2xl mx-auto">
              Upload your incident CSV file to get instant AI-powered categorization, team assignment, and detailed analysis
            </p>
          </div>



        {/* Upload Component */}
        {!results && !error && (
          <FileUpload 
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            onUploadStart={handleUploadStart}
          />
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">Processing your file with AI evaluation...</p>
            <p className="mt-2 text-sm text-gray-500">This may take a few moments depending on file size</p>
          </div>
        )}

        {/* Results Display */}
        {results && results.length > 0 && (
          <div className="space-y-8">
            {/* Header with Upload New File Button */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900">Analysis Complete</h2>
                <p className="text-gray-600 mt-1">
                  File: <span className="font-medium">{fileName}</span> • {results.length} incidents analyzed
                </p>
              </div>
              <button
                onClick={resetUpload}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                Upload New File
              </button>
            </div>

            {/* Summary Statistics */}
            <SummaryStatistics results={results} />

            {/* Detailed Results Table */}
            <ResultsTable results={results} />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <svg className="h-6 w-6 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h2 className="text-lg font-semibold text-red-900">
                Upload Failed
              </h2>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-red-200 mb-4">
              <p className="text-sm text-red-700">{error}</p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={resetUpload}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        </div>
      </div>
    </div>
  );
}